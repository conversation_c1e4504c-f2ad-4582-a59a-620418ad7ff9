package product

import (
	"net/http"

	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
	"sankyu-client/api"
)

type DetailRequest struct {
	CategoryID     int `json:"category_id" form:"category_id" query:"category_id"`
	UserCategoryID int `json:"user_category_id" form:"user_category_id" query:"user_category_id"`
}

type Category struct {
	ID                int    `json:"id"`
	KategoriProduk    string `json:"Kategori_Produk"`
	KodKategoriProduk string `json:"Kod_Kategori_Produk"`
	IsGoldbar         bool   `json:"is_goldbar"`
	ImgPath           string `json:"img_path"`
}

func DetailRequestHandler(db *gorm.DB) echo.HandlerFunc {
	return func(c echo.Context) error {
		var req DetailRequest

		if err := c.Bind(&req); err != nil || req.CategoryID == 0 || req.UserCategoryID == 0 {
			return c.JSON(http.StatusBadRequest, api.ApiResponse[any]{
				Status:     "Error",
				StatusCode: http.StatusBadRequest,
				Error:      true,
				Message:    "category_id and user_category_id are required",
			})
		}

		tx := db.Begin()
		defer func() {
			if r := recover(); r != nil {
				tx.Rollback()
			}
		}()

		var category Category
		if err := tx.Table("setting_database").
			Where("id = ? AND status = 1 AND is_publish = 1", req.CategoryID).
			Select("id, Kategori_Produk, Kod_Kategori_Produk, is_goldbar, img_path").
			First(&category).Error; err != nil {
			tx.Rollback()
			return c.JSON(http.StatusInternalServerError, api.ApiResponse[any]{
				Status:     "Error",
				StatusCode: http.StatusInternalServerError,
				Error:      true,
				Message:    "Failed to fetch category",
			})
		}

		var products []map[string]interface{}
		var priceErr error

		switch req.UserCategoryID {
		case 1, 2, 3, 4, 5:
			products, priceErr = getPriceByUserCategory(tx, req.CategoryID)
		default:
			tx.Rollback()
			return c.JSON(http.StatusBadRequest, api.ApiResponse[any]{
				Status:     "Error",
				StatusCode: http.StatusBadRequest,
				Error:      true,
				Message:    "Invalid user_category_id",
			})
		}

		if priceErr != nil {
			tx.Rollback()
			return c.JSON(http.StatusInternalServerError, api.ApiResponse[any]{
				Status:     "Error",
				StatusCode: http.StatusInternalServerError,
				Error:      true,
				Message:    priceErr.Error(),
			})
		}

		tx.Commit()

		return c.JSON(http.StatusOK, api.ApiResponse[map[string]interface{}]{
			Status:     "Success",
			StatusCode: http.StatusOK,
			Error:      false,
			Message:    "",
			Data: map[string]interface{}{
				"category": category,
				"products": products,
			},
		})
	}
}

func getPriceByUserCategory(db *gorm.DB, categoryID int) ([]map[string]interface{}, error) {
	var products []map[string]interface{}

	sharedFields := []string{
		"data_database.id",
		"data_database.receiving_Status",
		"data_database.cawangan",
		"data_database.dimension_Panjang",
		"data_database.dimension_Lebar",
		"data_database.dimension_Saiz",
		"data_database.kod_Purity",
		"data_database.kategori_Produk",
		"data_database.no_siri_Produk",
		"data_database.Berat",
		"data_database.Beza_Berat",
		"data_database.img_path",
		"data_database.img_path_2",
		"data_database.is_featured",
		"data_database.is_exclusive",
		"data_database.upah_g_comm_normal AS workmanship",
		"data_database.nota_lain_lain",
		"data_database.design",
		"data_database.code_design",
		"data_database.StatusItem",
		"data_database.is_sale",
		"data_database.diskaun_pelanggan_biasa AS discount",
		"data_database.kadar_diskaun_pelanggan_biasa AS promosi",
		"hargaemas.Harga_Pelanggan AS gold_price",
	}

	priceSQL := `
		CASE
			WHEN data_database.receiving_Status IN ('1','3','7','10')
				THEN data_database.code_Supplier - data_database.diskaun_pelanggan_biasa
			WHEN data_database.receiving_Status IN ('0','2','4','5','6','9')
				THEN data_database.Beza_Berat * (hargaemas.Harga_Pelanggan - data_database.diskaun_pelanggan_biasa)
		END AS price,
		CASE
			WHEN data_database.receiving_Status IN ('1','3','7','10')
				THEN data_database.HargaJualan_Member
			WHEN data_database.receiving_Status IN ('0','2','4','5','6','9')
				THEN data_database.Beza_Berat * hargaemas.Harga_Pelanggan
		END AS old_price
	`

	err := db.Table("data_database").
		Select(append(sharedFields, priceSQL)).
		Joins("JOIN hargaemas ON data_database.kod_Purity = hargaemas.Purity").
		Where("data_database.kategori_produk_ID = ?", categoryID).
		Where("data_database.is_publish = ?", 1).
		Where("hargaemas.cawangan = ?", "Online").
		Where("data_database.StatusItem IN ?", []int{10, 51}).
		Where("data_database.receiving_Status IS NOT NULL").
		Find(&products).Error

	return products, err
}
