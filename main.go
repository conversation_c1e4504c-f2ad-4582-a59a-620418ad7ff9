package main

import (
	"fmt"
	"log"
	"net/http"
	"os"

	"sankyu-client/api"

	"github.com/joho/godotenv"
	"github.com/labstack/echo/v4"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"sankyu-client/goldprice"
	"sankyu-client/api/product"
)

// Models
type Slider struct {
	ID    uint   `json:"id"`
	Image string `json:"image"`
}

type Category struct {
	ID                uint   `json:"id"`
	KategoriProduk    string `json:"Kategori_Produk"`
	KodKategoriProduk string `json:"Kod_Kategori_Produk"`
	ImgPath           string `json:"img_path"`
	IsGoldbar         bool   `json:"is_goldbar"`
	IsFeatured        bool   `json:"is_featured"`
}

type GoldPrice struct {
	Purity         string  `json:"Purity"`
	HargaPelanggan float64 `json:"Harga_Pelanggan"`
	HargaMember    float64 `json:"Harga_Member"`
	HargaPengedar  float64 `json:"Harga_Pengedar"`
	HargaRAF       float64 `json:"Harga_RAF"`
	HargaND        float64 `json:"harga_nd"`
	HargaTradeIn   float64 `json:"harga_trade_in"`
	HargaBuyback   float64 `json:"harga_buyback"`
}

type Point struct {
	ID     uint   `json:"id"`
	Detail string `json:"detail"` // Replace with your actual column
}

type Card struct {
	ID          uint   `json:"id"`
	Image       string `json:"image"`
	Title       string `json:"title"`
	Description string `json:"description"`
	ButtonName  string `json:"button_name"`
}

var db *gorm.DB

func main() {
	godotenv.Load()

	db = initDB()

	e := echo.New()

	// Register middleware
	e.GET("/", func(c echo.Context) error {
		return c.String(http.StatusOK, "API is running")
	})
	e.GET("/v1/landing-page", landingPageHandler, checkHeaders)
	e.GET("/v1/top-bar", api.TopBarHandler(db), checkHeaders)
	e.GET("/v1/contact-us", api.ContactUsHandler(db), checkHeaders)
	e.GET("/v1/general", api.SettingHandler(db), checkHeaders)
	e.GET("/v1/top-bar-no-goldbar", api.TopBarNoGoldbarHandler(db), checkHeaders)
	e.GET("/v1/goldbar/category-menu", api.CategoryMenuHandler(db), checkHeaders)
	e.GET("/v1/feature/setting", api.FeatureSettingHandler(), checkHeaders)
	e.GET("/v1/goldbar/category", api.GoldbarCategoryHandler(db), checkHeaders)
	e.GET("/v1/design/hot-selling", api.HotDesignHandler(db), checkHeaders)
	e.POST("/v1/product/featured", api.FeaturedProductHandler(db), checkHeaders)
	e.GET("/v1/gold-price/normal", goldprice.NormalGoldPriceHandler(db), checkHeaders)
	e.POST("/v1/category/details", product.DetailRequestHandler(db), checkHeaders)
	e.POST("/v1/product/category-no-design", product.NoDesignRequestHandler(db), checkHeaders)

	// Start server
	e.Logger.Fatal(e.Start(":3000"))
}

// Middleware for header auth
func checkHeaders(next echo.HandlerFunc) echo.HandlerFunc {
	return func(c echo.Context) error {
		key := c.Request().Header.Get("key")
		secret := c.Request().Header.Get("secret")

		if key == os.Getenv("API_KEY") && secret == os.Getenv("API_SECRET") {
			return next(c)
		}
		return c.String(http.StatusUnauthorized, "Unauthorized")
	}
}

// Main handler
func landingPageHandler(c echo.Context) error {
	tx := db.Begin()

	var slider []map[string]interface{}
	var category []map[string]interface{}
	var goldPrice []GoldPrice
	var point []map[string]interface{}
	var cardBig []map[string]interface{}

	var colName string
	err := db.Raw(`SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'ecomm_landing_slider' AND COLUMN_NAME = 'order_position'`).Scan(&colName).Error
	if err != nil {
		tx.Rollback()
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": err.Error()})
	}

	query := tx.Table("ecomm_landing_slider").Where("status = ? AND category = ?", 1, "desktop")
	if colName != "" {
		query = query.Order("order_position ASC")
	} else {
		query = query.Order("created_at ASC")
	}
	query.Find(&slider)

	tx.Table("setting_database").
		Where("kategori_Produk IS NOT NULL AND status = 1 AND img_path IS NOT NULL AND is_publish = 1").
		Select("id, Kategori_Produk, Kod_Kategori_Produk, img_path, is_goldbar, is_featured").
		Order("Kategori_Produk ASC").
		Scan(&category)

	tx.Table("hargaemas").
		Where("cawangan = ? AND is_publish = 1", "Online").
		Order("CONVERT(Purity, SIGNED) DESC").
		Select("Purity, Harga_Pelanggan, Harga_Member, Harga_Pengedar, Harga_RAF, harga_nd, harga_trade_in, harga_buyback").
		Scan(&goldPrice)

	tx.Table("ecomm_landing_point").
		Where("status = ?", 1).
		Scan(&point)

	tx.Table("ecomm_landing_card").
		Where("status = ? AND is_big_card = ?", 1, 1).
		Select("image, id, title, description, button_name").
		Scan(&cardBig)

	tx.Commit()

	// Final wrapped JSON response
	return c.JSON(http.StatusOK, map[string]interface{}{
		"status":      "Success",
		"status_code": 200,
		"desc":        "",
		"error":       false,
		"message":     "",
		"data": map[string]interface{}{
			"slider":     slider,
			"gold_price": goldPrice,
			"point":      point,
			"card_big":   cardBig,
			"category":   category,
		},
	})
}


// DB connection
func initDB() *gorm.DB {
	dbUser := os.Getenv("DB_USERNAME")
	dbPass := os.Getenv("DB_PASSWORD")
	dbHost := os.Getenv("DB_HOST")
	dbPort := os.Getenv("DB_PORT")
	dbName := os.Getenv("DB_DATABASE")

	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		dbUser, dbPass, dbHost, dbPort, dbName)

	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatalf("DB connection failed: %v", err)
	}
	return db
}
